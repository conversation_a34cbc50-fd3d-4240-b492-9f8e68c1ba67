#!/usr/bin/env python3
"""
Test script for the standalone chatbot classifier
Demonstrates various query types and expected classifications
"""

import os
from chatbot_script import ChatbotClassifier


def test_chatbot():
    """Test the chatbot with various query types"""
    
    # Get API key
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("Please set GEMINI_API_KEY environment variable")
        return
    
    try:
        # Initialize chatbot
        print("Initializing chatbot...")
        chatbot = ChatbotClassifier(api_key)
        
        # Test connection
        if not chatbot.test_connection():
            print("Failed to connect to Gemini API")
            return
        
        print("Chatbot initialized successfully!\n")
        
        # Test queries
        test_queries = [
            # FAQ queries (should be classified as 'faq')
            "How do I add a new property?",
            "Where do I enter the property address?",
            "How to upload documents?",
            "How to select property type?",
            "How to add amenities?",
            
            # SQL queries (should be classified as 'sql_query')
            "Show me all properties",
            "How many tenants do I have?",
            "What's the total rent collected this month?",
            "List all properties with rent above $2000",
            "Show me vacant properties",
            "What are the maintenance costs for last year?",
            
            # Ambiguous queries
            "Help me with my property",
            "I need information about rentals",
        ]
        
        print("Testing various query types:")
        print("=" * 60)
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n{i}. Query: '{query}'")
            
            result = chatbot.process_query(query)
            
            print(f"   Classification: {result['classification']['classification'].upper()}")
            print(f"   Confidence: {result['classification']['confidence']:.2f}")
            print(f"   Response: {result['response'][:100]}...")
            print(f"   Reasoning: {result['classification']['reasoning']}")
            
            if result['classification'].get('matched_intent_id'):
                print(f"   Matched Intent: {result['classification']['matched_intent_id']}")
        
        print("\n" + "=" * 60)
        print("Test completed!")
        
        # Summary
        faq_count = sum(1 for query in test_queries 
                       if chatbot.classify_query(query)['classification'] == 'faq')
        sql_count = len(test_queries) - faq_count
        
        print(f"\nSummary:")
        print(f"Total queries tested: {len(test_queries)}")
        print(f"FAQ classifications: {faq_count}")
        print(f"SQL classifications: {sql_count}")
        
    except Exception as e:
        print(f"Error during testing: {str(e)}")


def test_individual_query():
    """Test a single query interactively"""
    
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("Please set GEMINI_API_KEY environment variable")
        return
    
    try:
        chatbot = ChatbotClassifier(api_key)
        
        if not chatbot.test_connection():
            print("Failed to connect to Gemini API")
            return
        
        query = input("Enter a query to test: ").strip()
        if not query:
            print("No query entered")
            return
        
        print(f"\nTesting query: '{query}'")
        print("-" * 40)
        
        result = chatbot.process_query(query)
        
        print(f"Classification: {result['classification']['classification'].upper()}")
        print(f"Confidence: {result['classification']['confidence']:.2f}")
        print(f"Reasoning: {result['classification']['reasoning']}")
        
        if result['classification'].get('matched_intent_id'):
            print(f"Matched Intent: {result['classification']['matched_intent_id']}")
        
        print(f"\nResponse:")
        print(result['response'])
        
    except Exception as e:
        print(f"Error: {str(e)}")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "single":
        test_individual_query()
    else:
        test_chatbot()
