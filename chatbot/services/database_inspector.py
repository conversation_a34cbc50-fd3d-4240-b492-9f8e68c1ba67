"""
Database Inspector Service
Connects to PostgreSQL database and extracts schema information
"""

import logging
import psycopg2
from psycopg2.extras import RealDictCursor
from typing import Dict, List, Optional, Any
from django.conf import settings
import os

logger = logging.getLogger(__name__)


class DatabaseInspector:
    """Service to inspect PostgreSQL database schema and execute queries"""
    
    def __init__(self, db_config: Optional[Dict] = None):
        """Initialize database inspector with connection config"""
        if db_config:
            self.db_config = db_config
        else:
            # Use Django database settings or environment variables
            self.db_config = {
                'host': os.getenv('DB_HOST', 'localhost'),
                'port': os.getenv('DB_PORT', '5432'),
                'database': os.getenv('DB_NAME', 'rental-guru'),
                'user': os.getenv('DB_USER', 'postgres'),
                'password': os.getenv('DB_PASSWORD', ''),
            }
        
        self.connection = None
        logger.info("Database inspector initialized")

    def connect(self) -> bool:
        """Establish database connection"""
        try:
            self.connection = psycopg2.connect(**self.db_config)
            logger.info("Successfully connected to PostgreSQL database")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to database: {str(e)}")
            return False

    def disconnect(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()
            self.connection = None
            logger.info("Database connection closed")

    def get_database_schema(self) -> Dict:
        """
        Extract complete database schema information
        Returns dictionary with table structures, relationships, etc.
        """
        if not self.connection:
            if not self.connect():
                return {}
        
        try:
            schema = {}
            
            # Get all tables
            tables = self._get_tables()
            
            for table_name in tables:
                schema[table_name] = {
                    'columns': self._get_table_columns(table_name),
                    'foreign_keys': self._get_foreign_keys(table_name),
                    'indexes': self._get_table_indexes(table_name),
                    'description': self._get_table_description(table_name)
                }
            
            logger.info(f"Retrieved schema for {len(schema)} tables")
            return schema
            
        except Exception as e:
            logger.error(f"Error retrieving database schema: {str(e)}")
            return {}

    def execute_query(self, sql_query: str) -> Dict:
        """
        Execute SQL query and return results
        Returns dict with results, error info, and metadata
        """
        if not self.connection:
            if not self.connect():
                return {'error': 'Database connection failed', 'results': []}
        
        try:
            with self.connection.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute(sql_query)
                
                # Handle different query types
                if sql_query.strip().upper().startswith('SELECT'):
                    results = cursor.fetchall()
                    # Convert to list of dicts for JSON serialization
                    results = [dict(row) for row in results]
                else:
                    self.connection.commit()
                    results = {'affected_rows': cursor.rowcount}
                
                return {
                    'results': results,
                    'row_count': len(results) if isinstance(results, list) else cursor.rowcount,
                    'error': None
                }
                
        except Exception as e:
            logger.error(f"Error executing query: {str(e)}")
            if self.connection:
                self.connection.rollback()
            return {
                'results': [],
                'row_count': 0,
                'error': str(e)
            }

    def _get_tables(self) -> List[str]:
        """Get list of all tables in the database"""
        query = """
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
        ORDER BY table_name;
        """
        
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(query)
                return [row[0] for row in cursor.fetchall()]
        except Exception as e:
            logger.error(f"Error getting tables: {str(e)}")
            return []

    def _get_table_columns(self, table_name: str) -> List[Dict]:
        """Get column information for a specific table"""
        query = """
        SELECT 
            column_name,
            data_type,
            is_nullable,
            column_default,
            character_maximum_length,
            numeric_precision,
            numeric_scale
        FROM information_schema.columns 
        WHERE table_name = %s 
        AND table_schema = 'public'
        ORDER BY ordinal_position;
        """
        
        try:
            with self.connection.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute(query, (table_name,))
                columns = []
                
                for row in cursor.fetchall():
                    column_info = {
                        'name': row['column_name'],
                        'type': row['data_type'],
                        'nullable': row['is_nullable'] == 'YES',
                        'default': row['column_default'],
                    }
                    
                    # Add length/precision info if available
                    if row['character_maximum_length']:
                        column_info['max_length'] = row['character_maximum_length']
                    if row['numeric_precision']:
                        column_info['precision'] = row['numeric_precision']
                    if row['numeric_scale']:
                        column_info['scale'] = row['numeric_scale']
                    
                    columns.append(column_info)
                
                return columns
                
        except Exception as e:
            logger.error(f"Error getting columns for {table_name}: {str(e)}")
            return []

    def _get_foreign_keys(self, table_name: str) -> List[Dict]:
        """Get foreign key relationships for a table"""
        query = """
        SELECT
            kcu.column_name,
            ccu.table_name AS foreign_table_name,
            ccu.column_name AS foreign_column_name,
            rc.constraint_name
        FROM information_schema.table_constraints AS tc
        JOIN information_schema.key_column_usage AS kcu
            ON tc.constraint_name = kcu.constraint_name
            AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage AS ccu
            ON ccu.constraint_name = tc.constraint_name
            AND ccu.table_schema = tc.table_schema
        JOIN information_schema.referential_constraints AS rc
            ON tc.constraint_name = rc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
        AND tc.table_name = %s
        AND tc.table_schema = 'public';
        """
        
        try:
            with self.connection.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute(query, (table_name,))
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            logger.error(f"Error getting foreign keys for {table_name}: {str(e)}")
            return []

    def _get_table_indexes(self, table_name: str) -> List[Dict]:
        """Get index information for a table"""
        query = """
        SELECT
            indexname,
            indexdef
        FROM pg_indexes
        WHERE tablename = %s
        AND schemaname = 'public';
        """
        
        try:
            with self.connection.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute(query, (table_name,))
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            logger.error(f"Error getting indexes for {table_name}: {str(e)}")
            return []

    def _get_table_description(self, table_name: str) -> str:
        """Get table description/comment"""
        query = """
        SELECT obj_description(oid)
        FROM pg_class
        WHERE relname = %s
        AND relkind = 'r';
        """
        
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(query, (table_name,))
                result = cursor.fetchone()
                return result[0] if result and result[0] else f"Table for {table_name.replace('_', ' ')}"
        except Exception as e:
            logger.error(f"Error getting description for {table_name}: {str(e)}")
            return f"Table for {table_name.replace('_', ' ')}"

    def get_sample_data(self, table_name: str, limit: int = 5) -> List[Dict]:
        """Get sample data from a table for context"""
        query = f"SELECT * FROM {table_name} LIMIT %s;"
        
        try:
            with self.connection.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute(query, (limit,))
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            logger.error(f"Error getting sample data for {table_name}: {str(e)}")
            return []

    def test_connection(self) -> bool:
        """Test database connection"""
        try:
            if not self.connection:
                if not self.connect():
                    return False
            
            with self.connection.cursor() as cursor:
                cursor.execute("SELECT 1;")
                result = cursor.fetchone()
                return result[0] == 1
                
        except Exception as e:
            logger.error(f"Database connection test failed: {str(e)}")
            return False

    def __enter__(self):
        """Context manager entry"""
        self.connect()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.disconnect()
