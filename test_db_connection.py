#!/usr/bin/env python3
"""
Test script for PostgreSQL database connection and functionality
"""

import os
from chatbot_script import PostgreSQLConnector


def test_database_connection():
    """Test basic database connection and operations"""
    
    print("=== PostgreSQL Database Connection Test ===\n")
    
    # Get database configuration
    print("Enter your PostgreSQL database configuration:")
    db_config = {
        'host': input("Host (default: localhost): ").strip() or 'localhost',
        'port': int(input("Port (default: 5432): ").strip() or '5432'),
        'database': input("Database name: ").strip(),
        'user': input("Username: ").strip(),
        'password': input("Password: ").strip()
    }
    
    if not all([db_config['database'], db_config['user'], db_config['password']]):
        print("❌ Database configuration is incomplete.")
        return False
    
    try:
        # Initialize connector
        print("\n🔄 Initializing database connector...")
        db_connector = PostgreSQLConnector(db_config)
        
        # Test connection
        print("🔄 Testing database connection...")
        if not db_connector.connect():
            print("❌ Failed to connect to database")
            return False
        
        print("✅ Successfully connected to PostgreSQL database!")
        
        # Test basic query
        print("\n🔄 Testing basic query...")
        result = db_connector.execute_query("SELECT version()")
        if result['error']:
            print(f"❌ Query test failed: {result['error']}")
        else:
            print(f"✅ Query test successful!")
            print(f"   PostgreSQL Version: {result['results'][0]['version'][:50]}...")
        
        # Get list of tables
        print("\n🔄 Getting list of tables...")
        tables = db_connector.get_all_tables()
        if tables:
            print(f"✅ Found {len(tables)} tables:")
            for i, table in enumerate(tables[:10], 1):  # Show first 10 tables
                print(f"   {i}. {table}")
            if len(tables) > 10:
                print(f"   ... and {len(tables) - 10} more tables")
        else:
            print("⚠️  No tables found or unable to retrieve table list")
        
        # Test schema retrieval for first table
        if tables:
            print(f"\n🔄 Getting schema for table '{tables[0]}'...")
            schema = db_connector.get_table_schema(tables[0])
            if schema.get('error'):
                print(f"❌ Schema retrieval failed: {schema['error']}")
            else:
                print(f"✅ Schema retrieved successfully!")
                print(f"   Table: {schema['table_name']}")
                print(f"   Columns: {len(schema['columns'])}")
                for col in schema['columns'][:5]:  # Show first 5 columns
                    nullable = " (nullable)" if col['nullable'] else ""
                    print(f"     - {col['name']}: {col['type']}{nullable}")
                if len(schema['columns']) > 5:
                    print(f"     ... and {len(schema['columns']) - 5} more columns")
        
        # Test complete database schema
        print(f"\n🔄 Getting complete database schema...")
        full_schema = db_connector.get_database_schema()
        if full_schema:
            print(f"✅ Complete schema retrieved!")
            print(f"   Total tables with schema: {len(full_schema)}")
            total_columns = sum(len(table_info.get('columns', [])) for table_info in full_schema.values())
            print(f"   Total columns: {total_columns}")
        else:
            print("⚠️  Unable to retrieve complete database schema")
        
        # Cleanup
        print("\n🔄 Closing database connection...")
        db_connector.disconnect()
        print("✅ Database connection closed successfully!")
        
        print("\n🎉 All database tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error during database testing: {str(e)}")
        return False


def test_sample_queries():
    """Test sample SQL queries"""
    
    print("\n=== Sample SQL Queries Test ===\n")
    
    # Get database configuration
    db_config = {
        'host': input("Host (default: localhost): ").strip() or 'localhost',
        'port': int(input("Port (default: 5432): ").strip() or '5432'),
        'database': input("Database name: ").strip(),
        'user': input("Username: ").strip(),
        'password': input("Password: ").strip()
    }
    
    try:
        db_connector = PostgreSQLConnector(db_config)
        
        if not db_connector.connect():
            print("❌ Failed to connect to database")
            return
        
        # Sample queries to test
        sample_queries = [
            "SELECT COUNT(*) as total_tables FROM information_schema.tables WHERE table_schema = 'public'",
            "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' LIMIT 5",
            "SELECT current_database() as database_name",
            "SELECT current_user as current_user",
        ]
        
        print("Testing sample queries:")
        for i, query in enumerate(sample_queries, 1):
            print(f"\n{i}. Query: {query}")
            result = db_connector.execute_query(query)
            
            if result['error']:
                print(f"   ❌ Error: {result['error']}")
            else:
                print(f"   ✅ Success! Rows returned: {result['row_count']}")
                if result['results']:
                    print(f"   Result: {result['results'][0]}")
        
        # Interactive query testing
        print("\n" + "="*50)
        print("Interactive Query Testing")
        print("Enter SQL queries to test (type 'done' to finish):")
        
        while True:
            query = input("\nSQL> ").strip()
            
            if query.lower() in ['done', 'exit', 'quit']:
                break
            
            if not query:
                continue
            
            result = db_connector.execute_query(query)
            
            if result['error']:
                print(f"❌ Error: {result['error']}")
            else:
                print(f"✅ Success! Rows: {result['row_count']}")
                if result['results']:
                    # Show first few results
                    for i, row in enumerate(result['results'][:3], 1):
                        print(f"   Row {i}: {dict(row)}")
                    if len(result['results']) > 3:
                        print(f"   ... and {len(result['results']) - 3} more rows")
        
        db_connector.disconnect()
        print("\n✅ Query testing completed!")
        
    except Exception as e:
        print(f"❌ Error during query testing: {str(e)}")


def main():
    """Main function for database testing"""
    
    print("PostgreSQL Database Testing Tool")
    print("Choose an option:")
    print("1. Test database connection and basic operations")
    print("2. Test sample SQL queries")
    print("3. Both")
    
    choice = input("\nEnter your choice (1-3): ").strip()
    
    if choice == '1':
        test_database_connection()
    elif choice == '2':
        test_sample_queries()
    elif choice == '3':
        success = test_database_connection()
        if success:
            test_sample_queries()
    else:
        print("Invalid choice. Exiting.")


if __name__ == "__main__":
    main()
